#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sul<PERSON>};
use napi_derive::*;
use solana_entry::entry::Entry;
use solana_transaction::versioned::VersionedTransaction;

#[napi(object)]
pub struct SolanaEntry {
  pub num_hashes: i64,
  pub hash: Vec<u8>,
  pub transactions: Vec<Vec<u8>>,
}

impl From<Entry> for SolanaEntry {
  fn from(entry: Entry) -> Self {
    // Get raw transaction data without serializing
    let raw_transactions: Vec<Vec<u8>> = entry
      .transactions
      .iter()
      .map(|tx| {
        // Try to get the raw transaction data
        match tx {
          solana_transaction::versioned::VersionedTransaction::Legacy(legacy_tx) => {
            // For legacy transactions, we need to serialize to get the wire format
            bincode::serialize(legacy_tx).unwrap_or_default()
          }
          solana_transaction::versioned::VersionedTransaction::V0(v0_tx) => {
            // For V0 transactions, we need to serialize to get the wire format
            bincode::serialize(v0_tx).unwrap_or_default()
          }
        }
      })
      .collect();

    SolanaEntry {
      num_hashes: entry.num_hashes as i64,
      hash: entry.hash.to_bytes().to_vec(),
      transactions: raw_transactions,
    }
  }
}

#[napi]
pub fn decodeEntries(slot: JsBigInt, bytes: JsBuffer) -> Result<Vec<SolanaEntry>> {
  let _slot = slot; // Parameter for future use
  let buffer_data = bytes.into_value()?;

  if buffer_data.is_empty() {
    return Ok(Vec::new());
  }

  let entries: Vec<Entry> = bincode::deserialize(&buffer_data)
    .map_err(|e| napi::Error::from_reason(format!("Failed to deserialize entries: {}", e)))?;

  let decoded_entries: Vec<SolanaEntry> = entries.into_iter().map(SolanaEntry::from).collect();
  Ok(decoded_entries)
}
