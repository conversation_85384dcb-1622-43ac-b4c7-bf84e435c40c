#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Result};
use napi_derive::*;
use solana_entry::entry::Entry;

#[napi]
pub fn decodeEntries(slot: JsBigInt, bytes: <PERSON><PERSON><PERSON><PERSON><PERSON>) -> Result<Entry> {
  let buffer_data = bytes.into_value()?;
  let entry: Vec<Entry> = bincode::deserialize(&buffer_data)?;
}
