#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Result};
use napi_derive::*;
use solana_entry::entry::Entry;

// Mirror exact VersionedTransaction structure for napi
#[napi(object)]
pub struct NapiVersionedTransaction {
  // This will contain the raw transaction data
  pub data: Vec<u8>,
}

// Mirror exact Entry structure for napi
#[napi(object)]
pub struct NapiEntry {
  pub num_hashes: i64,
  pub hash: Vec<u8>,
  pub transactions: Vec<NapiVersionedTransaction>,
}

impl From<Entry> for NapiEntry {
  fn from(entry: Entry) -> Self {
    let napi_transactions: Vec<NapiVersionedTransaction> = entry
      .transactions
      .iter()
      .map(|tx| NapiVersionedTransaction {
        data: bincode::serialize(tx).unwrap_or_default(),
      })
      .collect();

    NapiEntry {
      num_hashes: entry.num_hashes as i64,
      hash: entry.hash.to_bytes().to_vec(),
      transactions: napi_transactions,
    }
  }
}

#[napi]
pub fn decodeEntries(slot: JsBigInt, bytes: JsBuffer) -> Result<Vec<NapiEntry>> {
  let _slot = slot; // Parameter for future use
  let buffer_data = bytes.into_value()?;

  if buffer_data.is_empty() {
    return Ok(Vec::new());
  }

  let entries: Vec<Entry> = bincode::deserialize(&buffer_data)
    .map_err(|e| napi::Error::from_reason(format!("Failed to deserialize entries: {}", e)))?;

  let napi_entries: Vec<NapiEntry> = entries.into_iter().map(NapiEntry::from).collect();
  Ok(napi_entries)
}
