#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Result};
use napi_derive::*;
use solana_entry::entry::Entry;

// Chỉ wrapper để napi có thể handle - KHÔNG serialize
#[napi(object)]
pub struct VersionedTransaction {
  // Chỉ để napi biết type, không chứa data
}

// Mirror chính xác Entry struct từ Rust
#[napi(object)]
pub struct SolanaEntry {
  pub num_hashes: i64,
  pub hash: Vec<u8>,
  pub transactions: Vec<VersionedTransaction>,
}

impl From<Entry> for SolanaEntry {
  fn from(entry: Entry) -> Self {
    // Chỉ map kiểu, KHÔNG serialize
    let transactions: Vec<VersionedTransaction> = entry
      .transactions
      .into_iter()
      .map(|_| VersionedTransaction {})
      .collect();

    SolanaEntry {
      num_hashes: entry.num_hashes as i64,
      hash: entry.hash.to_bytes().to_vec(),
      transactions,
    }
  }
}

#[napi]
pub fn decodeEntries(slot: JsBigInt, bytes: Js<PERSON><PERSON><PERSON>) -> Result<Vec<SolanaEntry>> {
  let _slot = slot;
  let buffer_data = bytes.into_value()?;

  if buffer_data.is_empty() {
    return Ok(Vec::new());
  }

  let entries: Vec<Entry> = bincode::deserialize(&buffer_data)
    .map_err(|e| napi::Error::from_reason(format!("Failed to deserialize entries: {}", e)))?;

  let solana_entries: Vec<SolanaEntry> = entries.into_iter().map(SolanaEntry::from).collect();
  Ok(solana_entries)
}
