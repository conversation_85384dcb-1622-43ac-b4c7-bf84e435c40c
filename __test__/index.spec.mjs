import test from 'ava'

import { decodeEntries } from '../index.js'

test('decodeEntries function exists', (t) => {
    t.is(typeof decodeEntries, 'function')
})

test('decodeEntries returns array', (t) => {
    // Test with empty buffer - should handle gracefully
    try {
        const result = decodeEntries(0n, Buffer.alloc(0))
        t.true(Array.isArray(result))
    } catch (error) {
        // Expected to fail with empty buffer, but function should exist
        t.true(error instanceof Error)
    }
})
