import test from 'ava'

import { decodeEntries } from '../index.js'

test('decodeEntries function exists', (t) => {
    t.is(typeof decodeEntries, 'function')
})

test('decodeEntries handles invalid data gracefully', (t) => {
    try {
        const result = decodeEntries(0n, Buffer.from([1, 2, 3, 4]))
        t.true(Array.isArray(result))
    } catch (error) {
        // Expected to fail with invalid data
        t.true(error instanceof Error)
        t.true(error.message.includes('Failed to deserialize'))
    }
})
