[package]
edition = "2021"
name = "shredstream-decoder"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
bincode = "1.3.3"
napi = { version = "2.12.2", default-features = false, features = [
    "napi8",
    "serde-json",
] }
napi-derive = "2.12.2"
serde = { version = "1.0.219", features = ["derive"] }
solana-entry = "2.2.7"

[build-dependencies]
napi-build = "2.0.1"

[profile.release]
lto = true
strip = "symbols"
